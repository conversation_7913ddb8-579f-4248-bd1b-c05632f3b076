from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, Enum as SQLAlchemyEnum
from sqlalchemy.orm import relationship
from app.core.database import Base
import enum
from datetime import datetime, timezone

# 创建一个函数来提供当前的UTC时间
def get_utc_now():
    return datetime.now(timezone.utc)

class RequirementType(str, enum.Enum):
    BUG_FIX = "Bug Fixed"
    HOT_FIX_BUG = "Hot Bug Fixed"
    NEW_FEATURE = "New Feature"

class Priority(str, enum.Enum):
    P0 = "P0"
    P1 = "P1"
    P2 = "P2"

class RequirementStatus(str, enum.Enum):
    DRAFT = "草稿"
    PENDING = "待处理"
    DEVELOPING = "开发中"
    TESTING = "测试中"
    VALIDATING = "验证中"
    COMPLETED = "已完成"

class RequirementAction(str, enum.Enum):
    SAVE_DRAFT = "保存草稿"
    PUBLISH = "发布需求"
    SUBMIT = "提交需求"
    CLAIM = "认领需求"
    SUBMIT_TO_TEST = "提交测试"
    WITHDRAW_TEST = "撤回测试"
    APPROVE_TEST = "通过测试"
    REJECT_TEST = "驳回测试"
    WITHDRAW_VALIDATION = "撤回验证"
    REJECT_VALIDATION = "驳回验证"

class PullRequestStatus(str, enum.Enum):
    OPEN = "开放中"
    MERGED = "已合并"
    CLOSED = "已关闭"

# 表名：Requirement（需求表）
class Requirement(Base):
    __tablename__ = "requirements"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键，自动递增整数")
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, comment="外键，关联项目表ID")
    requirement_code = Column(String(10), nullable=True, comment="需求编号，系统自动生成，格式为类型首字母+四位数字（如F0001）")
    title = Column(String(100), nullable=False, comment="需求标题")
    content = Column(Text, nullable=False, comment="需求内容，支持Markdown格式，包含Base64编码图片")
    type = Column(SQLAlchemyEnum(RequirementType), nullable=False, comment="需求类型")
    priority = Column(SQLAlchemyEnum(Priority), nullable=False, comment="优先级，枚举值包括P0、P1、P2")
    status = Column(SQLAlchemyEnum(RequirementStatus), nullable=False, default=RequirementStatus.PENDING, comment="状态，枚举值包括PENDING(待处理)、DEVELOPING(开发中)、TESTING(测试中)、VALIDATING(验证中)、COMPLETED(已完成)")
    start_date = Column(DateTime, nullable=False, comment="计划开始时间，默认提交日期")
    end_date = Column(DateTime, nullable=False, comment="计划完成时间，必填，需晚于开始日期")
    submitter_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="外键，关联用户表ID，记录提交人")
    submit_time = Column(DateTime, nullable=False, default=get_utc_now, comment="提交时间，自动记录")
    update_time = Column(DateTime, nullable=True, onupdate=get_utc_now, comment="更新时间，自动记录")
    git_branch = Column(String(200), nullable=True, comment="GitLab分支名称，包含前缀")

# 表名：RequirementAssignment（需求指派表）
class RequirementAssignment(Base):
    __tablename__ = "requirement_assignments"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键，自动递增整数")
    requirement_id = Column(Integer, ForeignKey("requirements.id"), nullable=False, comment="外键，关联需求表ID")
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="外键，关联用户表ID，记录指派的开发人员")
    assign_time = Column(DateTime, nullable=False, default=get_utc_now, comment="指派时间，自动记录")

# 表名：RequirementHistory（需求历史记录表）
class RequirementHistory(Base):
    __tablename__ = "requirement_histories"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键，自动递增整数")
    requirement_id = Column(Integer, ForeignKey("requirements.id"), nullable=False, comment="外键，关联需求表ID")
    current_status_code = Column(String(20), nullable=False, comment="当前状态代码，对应RequirementStatus的枚举值")
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="外键，关联用户表ID，记录执行行为的用户")
    action_time = Column(DateTime, nullable=False, default=get_utc_now, comment="操作时间，自动记录")
    action_code = Column(String(20), nullable=False, comment="动作代码，对应RequirementAction的枚举值")
    remark = Column(Text, nullable=True, comment="需求状态变更时用户填写的信息，例如审批意见")

# 表名：RequirementBranch（需求分支关联表）
class RequirementBranch(Base):
    __tablename__ = "requirement_branches"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键，自动递增整数")
    requirement_id = Column(Integer, ForeignKey("requirements.id"), nullable=False, comment="外键，关联需求表ID")
    branch_name = Column(String(100), nullable=False, comment="相关代码分支名称")
    is_main = Column(Boolean, default=False, nullable=False, comment="是否为主目标分支")

# 表名：RequirementPullRequest（需求PR关联表）
class RequirementPullRequest(Base):
    __tablename__ = "requirement_pull_requests"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键，自动递增整数")
    requirement_id = Column(Integer, ForeignKey("requirements.id"), nullable=False, comment="外键，关联需求表ID")
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, comment="外键，关联项目表ID")
    source_branch = Column(String(200), nullable=False, comment="源分支名称")
    target_branch = Column(String(100), nullable=False, comment="目标分支名称")
    pr_iid = Column(Integer, nullable=False, comment="GitLab中的PR IID")
    pr_url = Column(String(500), nullable=False, comment="PR的Web URL")
    status = Column(SQLAlchemyEnum(PullRequestStatus), nullable=False, default=PullRequestStatus.OPEN, comment="PR状态")
    create_time = Column(DateTime, nullable=False, default=get_utc_now, comment="创建时间")
    update_time = Column(DateTime, nullable=True, onupdate=get_utc_now, comment="更新时间")