import requests
from typing import List, Dict, Any, Optional
from app.core.config import Settings

settings = Settings()

class GitLabAPI:
    """用于与GitLab API交互的工具类"""

    def __init__(self, access_token: str = settings.GITLAB_ACCESS_TOKEN, api_url: str = settings.GITLAB_API_URL):
        self.access_token = access_token
        self.api_url = api_url
        self.headers = {
            "PRIVATE-TOKEN": access_token
        }

    def get_projects(self) -> List[Dict[str, Any]]:
        """获取GitLab上的所有项目列表"""
        url = f"{self.api_url}/projects"
        response = requests.get(url, headers=self.headers)

        if response.status_code != 200:
            raise Exception(f"获取GitLab项目列表失败: {response.status_code} - {response.text}")

        return response.json()

    def get_project(self, project_id: int) -> Optional[Dict[str, Any]]:
        """获取特定项目的详细信息"""
        url = f"{self.api_url}/projects/{project_id}"
        response = requests.get(url, headers=self.headers)

        if response.status_code != 200:
            return None

        return response.json()

    def get_branches(self, project_id: int) -> List[Dict[str, Any]]:
        """获取指定项目的分支列表"""
        all_branches = []
        page = 1
        per_page = 100  # GitLab API最大每页100条记录

        while True:
            url = f"{self.api_url}/projects/{project_id}/repository/branches"
            params = {
                'page': page,
                'per_page': per_page
            }
            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code != 200:
                raise Exception(f"获取GitLab项目分支列表失败: {response.status_code} - {response.text}")

            branches = response.json()
            if not branches:  # 如果没有更多分支，退出循环
                break

            all_branches.extend(branches)

            # 如果返回的分支数量少于per_page，说明已经是最后一页
            if len(branches) < per_page:
                break

            page += 1

        return all_branches

    def get_dev_branches(self, project_id: int) -> List[Dict[str, Any]]:
        result = []
        branches = self.get_branches(project_id)
        for branch in branches:  # 这里将list改为branches
            if branch['name'].startswith("dev/"):
                result.append(branch)
        return result

    def create_branch(self, project_id: int, branch_name: str, ref: str) -> Dict[str, Any]:
        """
        在GitLab项目中创建新分支

        Args:
            project_id: GitLab项目ID
            branch_name: 新分支名称
            ref: 源分支名称，新分支将基于此分支创建

        Returns:
            创建的分支信息
        """
        url = f"{self.api_url}/projects/{project_id}/repository/branches"
        data = {
            "branch": branch_name,
            "ref": ref
        }

        response = requests.post(url, headers=self.headers, json=data)

        if response.status_code not in [200, 201]:
            raise Exception(f"创建GitLab分支失败: {response.status_code} - {response.text}")

        return response.json()

    def delete_branch(self, project_id: int, branch_name: str) -> bool:
        """
        删除GitLab项目中的分支

        Args:
            project_id: GitLab项目ID
            branch_name: 要删除的分支名称

        Returns:
            是否删除成功
        """
        url = f"{self.api_url}/projects/{project_id}/repository/branches/{branch_name}"

        # 对分支名进行URL编码
        import urllib.parse
        encoded_branch_name = urllib.parse.quote(branch_name, safe='')
        url = f"{self.api_url}/projects/{project_id}/repository/branches/{encoded_branch_name}"

        response = requests.delete(url, headers=self.headers)

        if response.status_code not in [200, 204]:
            print(f"删除GitLab分支失败: {response.status_code} - {response.text}")
            return False

        return True

    def create_merge_request(self, project_id: int, source_branch: str, target_branch: str,
                            title: str = None, description: str = None) -> Dict[str, Any]:
        """
        在GitLab项目中创建合并请求(PR)

        Args:
            project_id: GitLab项目ID
            source_branch: 源分支名称
            target_branch: 目标分支名称
            title: 合并请求标题，默认为"Merge {source_branch} to {target_branch}"
            description: 合并请求描述，默认为空

        Returns:
            创建的合并请求信息
        """
        # 对项目ID进行URL编码
        import urllib.parse
        encoded_project_id = urllib.parse.quote(str(project_id), safe='')
        url = f"{self.api_url}/projects/{encoded_project_id}/merge_requests"

        # 如果没有提供标题，使用默认标题
        if not title:
            title = f"Merge {source_branch} to {target_branch}"

        # 准备请求数据
        data = {
            "source_branch": source_branch,
            "target_branch": target_branch,
            "title": title
        }

        # 如果提供了描述，添加到请求数据中
        if description:
            data["description"] = description

        # 发送请求创建合并请求
        response = requests.post(url, headers=self.headers, json=data)

        # 检查响应状态
        if response.status_code not in [200, 201]:
            raise Exception(f"创建GitLab合并请求失败: {response.status_code} - {response.text}")

        return response.json()

    def merge_pull_request(self, project_id: int, merge_request_iid: int,
                          merge_commit_message: str = None, force_merge: bool = False) -> Dict[str, Any]:
        """
        合并GitLab项目中的PR

        Args:
            project_id: GitLab项目ID
            merge_request_iid: 合并请求的IID
            merge_commit_message: 合并提交信息，默认为空
            force_merge: 是否强制合并，跳过状态检查等待

        Returns:
            合并结果信息
        """
        # 对项目ID进行URL编码
        import urllib.parse
        encoded_project_id = urllib.parse.quote(str(project_id), safe='')

        mr_url = f"{self.api_url}/projects/{encoded_project_id}/merge_requests/{merge_request_iid}"

        if not force_merge:
            # 等待合并请求检查完成，最多等待30秒
            import time
            max_wait_time = 30  # 最大等待时间（秒）
            check_interval = 2  # 检查间隔（秒）
            waited_time = 0

            while waited_time < max_wait_time:
                mr_response = requests.get(mr_url, headers=self.headers)

                if mr_response.status_code != 200:
                    raise Exception(f"获取合并请求信息失败: {mr_response.status_code} - {mr_response.text}")

                mr_info = mr_response.json()
                merge_status = mr_info.get('merge_status')
                state = mr_info.get('state')

                print(f"合并请求状态检查: state={state}, merge_status={merge_status}, 已等待={waited_time}秒")

                # 检查合并请求状态
                if state != 'opened':
                    raise Exception(f"合并请求状态不是opened，当前状态: {state}")

                # 如果状态是checking，继续等待
                if merge_status == 'checking':
                    print(f"GitLab正在检查合并请求，等待{check_interval}秒后重试...")
                    time.sleep(check_interval)
                    waited_time += check_interval
                    continue

                # 如果可以合并，跳出循环
                if merge_status in ['can_be_merged', 'unchecked']:
                    print(f"合并请求检查完成，状态: {merge_status}")
                    break

                # 如果是其他不可合并状态，抛出异常
                raise Exception(f"合并请求不能合并，状态: {merge_status}")

            # 如果超时仍在检查中，抛出异常
            if waited_time >= max_wait_time and mr_info.get('merge_status') == 'checking':
                raise Exception(f"等待合并请求检查超时（{max_wait_time}秒），当前状态: checking")
        else:
            # 强制合并模式，只做基本检查
            mr_response = requests.get(mr_url, headers=self.headers)

            if mr_response.status_code != 200:
                raise Exception(f"获取合并请求信息失败: {mr_response.status_code} - {mr_response.text}")

            mr_info = mr_response.json()
            state = mr_info.get('state')
            merge_status = mr_info.get('merge_status')

            print(f"强制合并模式: state={state}, merge_status={merge_status}")

            # 只检查基本状态
            if state != 'opened':
                raise Exception(f"合并请求状态不是opened，当前状态: {state}")

            print(f"强制合并，跳过状态检查，当前merge_status: {merge_status}")

        # 构建合并URL
        merge_url = f"{self.api_url}/projects/{encoded_project_id}/merge_requests/{merge_request_iid}/merge"

        # 准备请求数据
        data = {
            "should_remove_source_branch": False,  # 我们会手动删除分支
            "merge_when_pipeline_succeeds": False,  # 立即合并
        }
        if merge_commit_message:
            data["merge_commit_message"] = merge_commit_message

        # 发送请求合并PR，使用JSON格式
        response = requests.put(merge_url, headers=self.headers, json=data)

        # 检查响应状态
        if response.status_code not in [200, 201]:
            raise Exception(f"合并GitLab PR失败: {response.status_code} - {response.text}")

        return response.json()

    def close_pull_request(self, project_id: int, merge_request_iid: int) -> Dict[str, Any]:
        """
        关闭GitLab项目中的PR

        Args:
            project_id: GitLab项目ID
            merge_request_iid: 合并请求的IID

        Returns:
            关闭结果信息
        """
        # 对项目ID进行URL编码
        import urllib.parse
        encoded_project_id = urllib.parse.quote(str(project_id), safe='')
        url = f"{self.api_url}/projects/{encoded_project_id}/merge_requests/{merge_request_iid}"

        # 准备请求数据，设置状态为关闭
        data = {
            "state_event": "close"
        }

        # 发送请求关闭PR
        response = requests.put(url, headers=self.headers, json=data)

        # 检查响应状态
        if response.status_code not in [200, 201]:
            raise Exception(f"关闭GitLab PR失败: {response.status_code} - {response.text}")

        return response.json()

# 创建全局实例，方便导入使用
gitlab_api = GitLabAPI()